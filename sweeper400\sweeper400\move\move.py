# pyright: standard
"""
步进电机控制模块

该模块提供步进电机控制功能，包括：
- 电机参数获取
- 脉冲数与物理长度的换算
- 电机运动控制

主要功能：
1. 获取电机硬件参数
2. 建立脉冲数与物理长度(mm)的换算关系
3. 提供安全的电机控制接口
"""

import os
import time
from ctypes import *  # type: ignore
from typing import Optional
import logging

# 导入日志系统
from ..logger import get_logger

# 获取模块专用日志器
logger = get_logger(__name__)


class MotorController:
    """步进电机控制器类

    该类封装了MT-22E控制器的API调用，提供安全的电机控制功能。
    主要功能包括参数获取、单位换算和运动控制。
    """

    def __init__(
        self,
        step_angle: float = 1.8,
        subdivision: int = 20,
        pitch: float = 4.0,
        transmission_ratio: float = 1.0,
    ):
        """初始化电机控制器

        Args:
            step_angle: 步进角度（度），默认1.8°
            subdivision: 驱动器细分数，默认20
            pitch: 螺距（mm），默认4mm
            transmission_ratio: 传动比，默认1.0（无传动）
        """
        self._dll_path = os.path.join(os.path.dirname(__file__), "MT_API.dll")
        self._api = None
        self._is_initialized = False
        self._is_connected = False

        # 电机参数（在初始化时必须指定，不能为None）
        self._step_angle: float = step_angle
        self._subdivision: int = subdivision
        self._pitch: float = pitch
        self._transmission_ratio: float = transmission_ratio

        logger.info(
            f"电机控制器实例已创建，参数: 步进角度={step_angle}°, "
            f"细分数={subdivision}, 螺距={pitch}mm, 传动比={transmission_ratio}"
        )

    def initialize(self) -> bool:
        """初始化API库

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 加载DLL
            self._api = windll.LoadLibrary(self._dll_path)
            logger.info(f"成功加载DLL: {self._dll_path}")

            # 设置函数参数类型
            self._setup_api_types()

            # 初始化API
            result = self._api.MT_Init()
            if result == 0:
                self._is_initialized = True
                logger.info("API初始化成功")
                return True
            else:
                logger.error(f"API初始化失败，错误码: {result}")
                return False

        except Exception as e:
            logger.error(f"初始化过程中发生异常: {e}")
            return False

    def _setup_api_types(self):
        """设置API函数的参数类型"""
        # 硬件信息相关函数
        self._api.MT_Get_Product_Resource.argtypes = [POINTER(c_int32)]
        self._api.MT_Get_Product_ID.argtypes = [c_char_p]
        self._api.MT_Get_Product_SN.argtypes = [c_char_p]

        # 辅助计算函数
        self._api.MT_Help_Step_Line_Real_To_Steps.argtypes = [
            c_double,
            c_int32,
            c_double,
            c_double,
            c_double,
        ]
        self._api.MT_Help_Step_Line_Real_To_Steps.restype = c_int32

        self._api.MT_Help_Step_Line_Steps_To_Real.argtypes = [
            c_double,
            c_int32,
            c_double,
            c_double,
            c_int32,
        ]
        self._api.MT_Help_Step_Line_Steps_To_Real.restype = c_double

        # 通信相关函数
        self._api.MT_Open_UART.argtypes = [c_char_p]
        self._api.MT_Open_USB.argtypes = []

        # 零位模式相关函数
        self._api.MT_Set_Axis_Mode_Home.argtypes = [c_uint16]
        self._api.MT_Set_Axis_Mode_Home_Encoder_Index.argtypes = [c_uint16]
        self._api.MT_Set_Axis_Mode_Home_Encoder_Home_Switch.argtypes = [c_uint16]
        self._api.MT_Set_Axis_Home_V.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Home_Acc.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Home_Dec.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Home_Stop.argtypes = [c_uint16]
        self._api.MT_Set_Axis_Halt.argtypes = [c_uint16]

        # 状态查询相关函数
        self._api.MT_Get_Axis_Status_Run.argtypes = [c_uint16, POINTER(c_int32)]
        self._api.MT_Get_Axis_Status_Zero.argtypes = [c_uint16, POINTER(c_int32)]
        self._api.MT_Get_Axis_Mode.argtypes = [c_uint16, POINTER(c_int32)]
        self._api.MT_Get_Encoder_Num.argtypes = [POINTER(c_int32)]

        # 位置相关函数
        self._api.MT_Get_Axis_Software_P_Now.argtypes = [c_uint16, POINTER(c_int32)]
        self._api.MT_Set_Axis_Software_P.argtypes = [c_uint16, c_int32]

        # 位置模式相关函数
        self._api.MT_Set_Axis_Mode_Position.argtypes = [c_uint16]
        self._api.MT_Set_Axis_Position_Acc.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Position_Dec.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Position_V_Max.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Position_P_Target_Rel.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Position_P_Target_Abs.argtypes = [c_uint16, c_int32]
        self._api.MT_Set_Axis_Position_Stop.argtypes = [c_uint16]

        logger.debug("API函数参数类型设置完成")

    def connect_usb(self) -> bool:
        """通过USB连接控制器

        Returns:
            bool: 连接是否成功
        """
        if not self._is_initialized:
            logger.error("API未初始化，无法连接")
            return False

        try:
            result = self._api.MT_Open_USB()
            if result == 0:
                # 检查连接状态
                check_result = self._api.MT_Check()
                if check_result == 0:
                    self._is_connected = True
                    logger.info("USB连接成功")
                    return True
                else:
                    logger.error(f"连接检查失败，错误码: {check_result}")
                    return False
            else:
                logger.error(f"USB连接失败，错误码: {result}")
                return False

        except Exception as e:
            logger.error(f"USB连接过程中发生异常: {e}")
            return False

    def get_hardware_info(self) -> dict[str, str | int]:
        """获取硬件信息

        Returns:
            dict: 包含硬件信息的字典
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法获取硬件信息")
            return {}

        info = {}

        try:
            # 获取产品资源信息
            resource = c_int32(0)
            result = self._api.MT_Get_Product_Resource(byref(resource))
            if result == 0:
                info["resource"] = resource.value
                logger.info(f"产品资源信息: 0x{resource.value:08X}")
            else:
                logger.warning(f"获取产品资源失败，错误码: {result}")

            # 获取产品ID
            product_id = create_string_buffer(16)
            result = self._api.MT_Get_Product_ID(product_id)
            if result == 0:
                info["product_id"] = product_id.value.decode("gbk")
                logger.info(f"产品ID: {info['product_id']}")
            else:
                logger.warning(f"获取产品ID失败，错误码: {result}")

            # 获取产品序列号
            serial_number = create_string_buffer(12)
            result = self._api.MT_Get_Product_SN(serial_number)
            if result == 0:
                info["serial_number"] = serial_number.value.decode("gbk")
                logger.info(f"产品序列号: {info['serial_number']}")
            else:
                logger.warning(f"获取产品序列号失败，错误码: {result}")

        except Exception as e:
            logger.error(f"获取硬件信息时发生异常: {e}")

        return info

    def update_motor_parameters(
        self,
        step_angle: Optional[float] = None,
        subdivision: Optional[int] = None,
        pitch: Optional[float] = None,
        transmission_ratio: Optional[float] = None,
    ):
        """更新电机参数（可选择性更新）

        Args:
            step_angle: 步进角度（度），如果为None则保持当前值
            subdivision: 驱动器细分数，如果为None则保持当前值
            pitch: 螺距（mm），如果为None则保持当前值
            transmission_ratio: 传动比，如果为None则保持当前值
        """
        if step_angle is not None:
            self._step_angle = step_angle
        if subdivision is not None:
            self._subdivision = subdivision
        if pitch is not None:
            self._pitch = pitch
        if transmission_ratio is not None:
            self._transmission_ratio = transmission_ratio

        logger.info(
            f"电机参数已更新: 步进角度={self._step_angle}°, 细分数={self._subdivision}, "
            f"螺距={self._pitch}mm, 传动比={self._transmission_ratio}"
        )

    def get_motor_parameters(self) -> dict[str, float | int]:
        """获取当前电机参数

        Returns:
            dict: 包含电机参数的字典
        """
        return {
            "step_angle": self._step_angle,
            "subdivision": self._subdivision,
            "pitch": self._pitch,
            "transmission_ratio": self._transmission_ratio,
        }

    def _check_parameters(self) -> bool:
        """检查电机参数是否有效

        Returns:
            bool: 参数是否有效
        """
        # 参数在初始化时已设置，不会为None，只需检查是否为有效值
        if self._step_angle <= 0 or self._subdivision <= 0 or self._pitch <= 0:
            logger.error("电机参数无效，无法进行换算")
            return False
        return True

    def mm_to_steps(self, distance_mm: float) -> int:
        """将物理距离（mm）转换为脉冲数

        Args:
            distance_mm: 物理距离（mm）

        Returns:
            int: 对应的脉冲数

        Raises:
            ValueError: 当参数未设置时
        """
        if not self._check_parameters():
            raise ValueError("电机参数未完整设置")

        if not self._is_connected:
            logger.warning("未连接到控制器，使用本地计算")

        try:
            # 参数在初始化时已设置，不会为None
            if self._is_connected and self._api is not None:
                # 使用API函数进行转换
                steps = self._api.MT_Help_Step_Line_Real_To_Steps(
                    self._step_angle,
                    self._subdivision,
                    self._pitch,
                    self._transmission_ratio,
                    distance_mm,
                )
                logger.debug(f"API转换: {distance_mm}mm -> {steps}步")
            else:
                # 本地计算：steps = (distance_mm / pitch) * (360 / step_angle) * subdivision * transmission_ratio
                steps_per_revolution = (360.0 / self._step_angle) * self._subdivision
                steps = int(
                    (distance_mm / self._pitch)
                    * steps_per_revolution
                    * self._transmission_ratio
                )
                logger.debug(f"本地计算: {distance_mm}mm -> {steps}步")

            return steps

        except Exception as e:
            logger.error(f"距离转换脉冲数时发生异常: {e}")
            raise

    def steps_to_mm(self, steps: int) -> float:
        """将脉冲数转换为物理距离（mm）

        Args:
            steps: 脉冲数

        Returns:
            float: 对应的物理距离（mm）

        Raises:
            ValueError: 当参数未设置时
        """
        if not self._check_parameters():
            raise ValueError("电机参数未完整设置")

        if not self._is_connected:
            logger.warning("未连接到控制器，使用本地计算")

        try:
            # 参数在初始化时已设置，不会为None
            if self._is_connected and self._api is not None:
                # 使用API函数进行转换
                distance = self._api.MT_Help_Step_Line_Steps_To_Real(
                    self._step_angle,
                    self._subdivision,
                    self._pitch,
                    self._transmission_ratio,
                    steps,
                )
                logger.debug(f"API转换: {steps}步 -> {distance}mm")
            else:
                # 本地计算：distance = (steps / steps_per_revolution) * pitch / transmission_ratio
                steps_per_revolution = (360.0 / self._step_angle) * self._subdivision
                distance = (
                    (steps / steps_per_revolution)
                    * self._pitch
                    / self._transmission_ratio
                )
                logger.debug(f"本地计算: {steps}步 -> {distance}mm")

            return distance

        except Exception as e:
            logger.error(f"脉冲数转换距离时发生异常: {e}")
            raise

    def get_conversion_factor(self) -> float:
        """获取换算系数（步/mm）

        Returns:
            float: 每毫米对应的脉冲数

        Raises:
            ValueError: 当参数未设置时
        """
        if not self._check_parameters():
            raise ValueError("电机参数未完整设置")

        # 参数在初始化时已设置，不会为None
        # 计算每毫米的脉冲数
        steps_per_revolution = (360.0 / self._step_angle) * self._subdivision
        steps_per_mm = (steps_per_revolution * self._transmission_ratio) / self._pitch

        logger.info(f"换算系数: {steps_per_mm:.2f} 步/mm")
        return steps_per_mm

    def check_encoder_available(self) -> bool:
        """检查编码器/光栅尺是否可用

        Returns:
            bool: 编码器/光栅尺是否可用
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法检查编码器状态")
            return False

        try:
            encoder_num = c_int32(0)
            result = self._api.MT_Get_Encoder_Num(byref(encoder_num))
            if result == 0 and encoder_num.value > 0:
                logger.info(f"检测到 {encoder_num.value} 个编码器/光栅尺接口")
                return True
            else:
                logger.info("未检测到可用的编码器/光栅尺接口")
                return False
        except Exception as e:
            logger.error(f"检查编码器状态时发生异常: {e}")
            return False

    def home_calibration(
        self,
        axis: int = 0,
        home_speed: int = -2000,
        acceleration: int = 2000,
        deceleration: int = 2000,
        use_encoder: Optional[bool] = None,
        timeout_seconds: float = 10.0,
    ) -> bool:
        """执行零位校准

        Args:
            axis: 轴序号，0为X轴，1为Y轴
            home_speed: 零位模式速度（Hz/s），负值表示负向查找
            acceleration: 加速度（Hz/s²）
            deceleration: 减速度（Hz/s²）
            use_encoder: 是否使用编码器模式，None表示自动检测
            timeout_seconds: 超时时间（秒）

        Returns:
            bool: 校准是否成功
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法执行零位校准")
            return False

        try:
            # 停止当前运动
            self._api.MT_Set_Axis_Halt(axis)

            # 自动检测编码器可用性
            if use_encoder is None:
                use_encoder = self.check_encoder_available()

            # 设置零位模式
            if use_encoder:
                logger.info(f"轴{axis}: 使用闭环零位模式（编码器/光栅尺）")
                result = self._api.MT_Set_Axis_Mode_Home_Encoder_Index(axis)
            else:
                logger.info(f"轴{axis}: 使用开环零位模式（限位开关）")
                result = self._api.MT_Set_Axis_Mode_Home(axis)

            if result != 0:
                logger.error(f"设置零位模式失败，错误码: {result}")
                return False

            # 设置零位参数
            self._api.MT_Set_Axis_Home_Acc(axis, acceleration)
            self._api.MT_Set_Axis_Home_Dec(axis, deceleration)
            self._api.MT_Set_Axis_Home_V(axis, home_speed)

            logger.info(
                f"轴{axis}: 开始零位校准，速度={home_speed}Hz/s, "
                f"加速度={acceleration}Hz/s², 减速度={deceleration}Hz/s²"
            )

            # 等待校准完成
            import time

            start_time = time.time()
            logger.info(
                f"轴{axis}: 等待零位校准完成，最大等待时间{timeout_seconds}秒..."
            )

            while time.time() - start_time < timeout_seconds:
                # 检查运行状态
                run_status = c_int32(0)
                result = self._api.MT_Get_Axis_Status_Run(axis, byref(run_status))

                if result == 0 and run_status.value == 0:
                    # 电机已停止，等待系统处理零位设置
                    logger.info(f"轴{axis}: 电机已停止，等待系统处理零位设置...")
                    time.sleep(1.0)  # 等待1秒让系统处理零位设置

                    # 检查当前位置是否已设为0（零位校准成功的标志）
                    current_pos = c_int32(0)
                    pos_result = self._api.MT_Get_Axis_Software_P_Now(
                        axis, byref(current_pos)
                    )

                    if pos_result == 0:
                        logger.info(f"轴{axis}: 当前位置 = {current_pos.value}")
                        if abs(current_pos.value) < 100:  # 允许小的误差
                            logger.info(
                                f"轴{axis}: 零位校准成功，当前位置已设为{current_pos.value}"
                            )
                            return True
                        else:
                            # 如果位置不为0，可能是碰到限位但未找到零位开关
                            # 根据API文档，此时应该手动设置当前位置为0
                            logger.info(
                                f"轴{axis}: 电机已到达限位，手动设置当前位置为零位"
                            )
                            self._api.MT_Set_Axis_Software_P(axis, 0)
                            time.sleep(0.5)  # 等待设置生效

                            # 再次检查位置
                            self._api.MT_Get_Axis_Software_P_Now(
                                axis, byref(current_pos)
                            )
                            logger.info(
                                f"轴{axis}: 零位设置后的位置 = {current_pos.value}"
                            )
                            return True
                    else:
                        logger.warning(
                            f"轴{axis}: 无法读取当前位置，错误码: {pos_result}"
                        )
                        return False

                time.sleep(0.2)  # 200ms检查间隔

            # 超时处理
            logger.warning(f"轴{axis}: 零位校准超时，强制停止")
            self._api.MT_Set_Axis_Home_Stop(axis)
            return False

        except Exception as e:
            logger.error(f"轴{axis}: 零位校准过程中发生异常: {e}")
            try:
                self._api.MT_Set_Axis_Home_Stop(axis)
            except:
                pass
            return False

    def get_axis_status(self, axis: int = 0) -> dict[str, int | bool]:
        """获取轴状态信息

        Args:
            axis: 轴序号

        Returns:
            dict: 包含轴状态信息的字典
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法获取轴状态")
            return {}

        status = {}

        try:
            # 运行状态
            run_status = c_int32(0)
            if self._api.MT_Get_Axis_Status_Run(axis, byref(run_status)) == 0:
                status["is_running"] = bool(run_status.value)

            # 零位状态
            zero_status = c_int32(0)
            if self._api.MT_Get_Axis_Status_Zero(axis, byref(zero_status)) == 0:
                status["at_zero"] = bool(zero_status.value)

            # 工作模式
            mode = c_int32(0)
            if self._api.MT_Get_Axis_Mode(axis, byref(mode)) == 0:
                mode_names = {0: "零位模式", 1: "速度模式", 2: "位置模式"}
                status["mode"] = mode_names.get(mode.value, f"未知模式({mode.value})")
                status["mode_code"] = mode.value

        except Exception as e:
            logger.error(f"获取轴{axis}状态时发生异常: {e}")

        return status

    def move_relative_mm(
        self,
        axis: int,
        distance_mm: float,
        max_speed: int = 5000,
        acceleration: int = 3000,
        deceleration: int = 3000,
        timeout_seconds: float = 30.0,
    ) -> bool:
        """相对运动指定距离（毫米）

        Args:
            axis: 轴序号，0为X轴，1为Y轴
            distance_mm: 运动距离（毫米），正值为正向，负值为负向
            max_speed: 最大速度（Hz/s）
            acceleration: 加速度（Hz/s²）
            deceleration: 减速度（Hz/s²）
            timeout_seconds: 超时时间（秒）

        Returns:
            bool: 运动是否成功完成
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法执行运动")
            return False

        try:
            # 将距离转换为脉冲数
            steps = self.mm_to_steps(distance_mm)
            logger.info(f"轴{axis}: 准备运动 {distance_mm}mm ({steps}步)")

            # 停止当前运动
            self._api.MT_Set_Axis_Halt(axis)
            time.sleep(0.1)

            # 切换到位置模式
            result = self._api.MT_Set_Axis_Mode_Position(axis)
            if result != 0:
                logger.error(f"轴{axis}: 设置位置模式失败，错误码: {result}")
                return False

            # 设置运动参数
            self._api.MT_Set_Axis_Position_Acc(axis, acceleration)
            self._api.MT_Set_Axis_Position_Dec(axis, deceleration)
            self._api.MT_Set_Axis_Position_V_Max(axis, max_speed)

            logger.info(
                f"轴{axis}: 运动参数设置完成 - 最大速度={max_speed}Hz/s, "
                f"加速度={acceleration}Hz/s², 减速度={deceleration}Hz/s²"
            )

            # 获取运动前位置
            start_pos = c_int32(0)
            self._api.MT_Get_Axis_Software_P_Now(axis, byref(start_pos))
            logger.info(f"轴{axis}: 运动前位置 = {start_pos.value}步")

            # 执行相对运动
            result = self._api.MT_Set_Axis_Position_P_Target_Rel(axis, steps)
            if result != 0:
                logger.error(f"轴{axis}: 启动相对运动失败，错误码: {result}")
                return False

            logger.info(f"轴{axis}: 开始相对运动...")

            # 等待运动完成
            import time

            start_time = time.time()

            while time.time() - start_time < timeout_seconds:
                # 检查运行状态
                run_status = c_int32(0)
                result = self._api.MT_Get_Axis_Status_Run(axis, byref(run_status))

                if result == 0 and run_status.value == 0:
                    # 运动已完成
                    end_pos = c_int32(0)
                    self._api.MT_Get_Axis_Software_P_Now(axis, byref(end_pos))

                    actual_steps = end_pos.value - start_pos.value
                    actual_distance = self.steps_to_mm(actual_steps)

                    logger.info(f"轴{axis}: 运动完成")
                    logger.info(f"轴{axis}: 运动后位置 = {end_pos.value}步")
                    logger.info(
                        f"轴{axis}: 实际运动 = {actual_steps}步 ({actual_distance:.3f}mm)"
                    )
                    logger.info(f"轴{axis}: 目标运动 = {steps}步 ({distance_mm:.3f}mm)")

                    # 检查运动精度（允许1%的误差）
                    error_ratio = (
                        abs(actual_steps - steps) / abs(steps) if steps != 0 else 0
                    )
                    if error_ratio < 0.01:
                        logger.info(
                            f"轴{axis}: 运动精度良好，误差比例 = {error_ratio:.4f}"
                        )
                        return True
                    else:
                        logger.warning(
                            f"轴{axis}: 运动精度较差，误差比例 = {error_ratio:.4f}"
                        )
                        return True  # 仍然认为运动成功，但记录警告

                time.sleep(0.1)  # 100ms检查间隔

            # 超时处理
            logger.warning(f"轴{axis}: 运动超时，强制停止")
            self._api.MT_Set_Axis_Position_Stop(axis)
            return False

        except Exception as e:
            logger.error(f"轴{axis}: 运动过程中发生异常: {e}")
            try:
                self._api.MT_Set_Axis_Position_Stop(axis)
            except:
                pass
            return False

    def move_absolute_mm(
        self,
        axis: int,
        position_mm: float,
        max_speed: int = 5000,
        acceleration: int = 3000,
        deceleration: int = 3000,
        timeout_seconds: float = 30.0,
    ) -> bool:
        """绝对运动到指定位置（毫米）

        Args:
            axis: 轴序号，0为X轴，1为Y轴
            position_mm: 目标位置（毫米）
            max_speed: 最大速度（Hz/s）
            acceleration: 加速度（Hz/s²）
            deceleration: 减速度（Hz/s²）
            timeout_seconds: 超时时间（秒）

        Returns:
            bool: 运动是否成功完成
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法执行运动")
            return False

        try:
            # 获取当前位置
            current_pos = c_int32(0)
            self._api.MT_Get_Axis_Software_P_Now(axis, byref(current_pos))
            current_mm = self.steps_to_mm(current_pos.value)

            # 计算相对距离
            distance_mm = position_mm - current_mm

            logger.info(
                f"轴{axis}: 绝对运动 - 当前位置={current_mm:.3f}mm, 目标位置={position_mm:.3f}mm"
            )
            logger.info(f"轴{axis}: 需要运动距离={distance_mm:.3f}mm")

            # 调用相对运动方法
            return self.move_relative_mm(
                axis=axis,
                distance_mm=distance_mm,
                max_speed=max_speed,
                acceleration=acceleration,
                deceleration=deceleration,
                timeout_seconds=timeout_seconds,
            )

        except Exception as e:
            logger.error(f"轴{axis}: 绝对运动过程中发生异常: {e}")
            return False

    def get_current_position_mm(self, axis: int) -> float:
        """获取当前位置（毫米）

        Args:
            axis: 轴序号，0为X轴，1为Y轴

        Returns:
            float: 当前位置（毫米）
        """
        if not self._is_connected:
            logger.error("未连接到控制器，无法获取位置")
            return 0.0

        try:
            current_pos = c_int32(0)
            result = self._api.MT_Get_Axis_Software_P_Now(axis, byref(current_pos))

            if result == 0:
                position_mm = self.steps_to_mm(current_pos.value)
                logger.debug(
                    f"轴{axis}: 当前位置 = {current_pos.value}步 ({position_mm:.3f}mm)"
                )
                return position_mm
            else:
                logger.error(f"轴{axis}: 获取位置失败，错误码: {result}")
                return 0.0

        except Exception as e:
            logger.error(f"轴{axis}: 获取位置时发生异常: {e}")
            return 0.0

    def disconnect(self):
        """断开连接"""
        if self._is_connected and self._api:
            try:
                self._api.MT_Close_USB()
                self._api.MT_Close_UART()
                self._api.MT_Close_Net()
                self._is_connected = False
                logger.info("已断开控制器连接")
            except Exception as e:
                logger.error(f"断开连接时发生异常: {e}")

    def cleanup(self):
        """清理资源"""
        self.disconnect()
        if self._is_initialized and self._api:
            try:
                self._api.MT_DeInit()
                self._is_initialized = False
                logger.info("API资源已释放")
            except Exception as e:
                logger.error(f"清理资源时发生异常: {e}")

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        self.cleanup()


def create_motor_controller() -> MotorController:
    """创建电机控制器实例的工厂函数

    Returns:
        MotorController: 电机控制器实例
    """
    return MotorController()
