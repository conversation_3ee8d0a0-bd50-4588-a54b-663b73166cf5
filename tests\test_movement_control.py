# pyright: standard
"""
测试运动控制功能的脚本

该脚本用于测试MotorController类的运动控制功能，
包括相对运动和绝对运动，以及验证X轴和Y轴的参数一致性
"""

from sweeper400.move.move import MotorController
import time


def test_movement_control():
    """测试运动控制功能"""
    print("=== 运动控制功能测试 ===")
    
    # 创建控制器实例
    controller = MotorController()
    print(f"电机参数: {controller.get_motor_parameters()}")
    
    # 初始化和连接
    if not controller.initialize():
        print("API初始化失败")
        return False
        
    if not controller.connect_usb():
        print("USB连接失败")
        return False
        
    print("控制器连接成功")
    
    # 获取硬件信息
    hw_info = controller.get_hardware_info()
    print(f"硬件信息: {hw_info}")
    
    # 查看初始位置
    print("\n--- 初始位置 ---")
    x_pos = controller.get_current_position_mm(0)
    y_pos = controller.get_current_position_mm(1)
    print(f"X轴当前位置: {x_pos:.3f}mm")
    print(f"Y轴当前位置: {y_pos:.3f}mm")
    
    # 测试距离（毫米）
    test_distance = 100.0  # 100mm
    
    print(f"\n=== 开始运动测试：{test_distance}mm ===")
    print("请确认电机可以安全运动，然后按Enter继续...")
    input()
    
    # 测试X轴正向运动
    print(f"\n--- X轴正向运动 {test_distance}mm ---")
    x_success = controller.move_relative_mm(
        axis=0,
        distance_mm=test_distance,
        max_speed=3000,
        acceleration=2000,
        deceleration=2000,
        timeout_seconds=30.0
    )
    
    print(f"X轴运动结果: {'成功' if x_success else '失败'}")
    
    # 获取X轴运动后位置
    x_pos_after = controller.get_current_position_mm(0)
    x_actual_distance = x_pos_after - x_pos
    print(f"X轴运动前位置: {x_pos:.3f}mm")
    print(f"X轴运动后位置: {x_pos_after:.3f}mm")
    print(f"X轴实际运动距离: {x_actual_distance:.3f}mm")
    print(f"X轴目标运动距离: {test_distance:.3f}mm")
    print(f"X轴运动误差: {abs(x_actual_distance - test_distance):.3f}mm")
    
    if x_success:
        print("\n请手动测量X轴的实际运动距离，然后按Enter继续...")
        user_measurement = input("请输入实际测量的X轴运动距离（mm）: ")
        try:
            measured_distance = float(user_measurement)
            software_error = abs(x_actual_distance - test_distance)
            hardware_error = abs(measured_distance - test_distance)
            print(f"软件计算误差: {software_error:.3f}mm")
            print(f"实际测量误差: {hardware_error:.3f}mm")
            print(f"软件与实测差异: {abs(x_actual_distance - measured_distance):.3f}mm")
        except ValueError:
            print("输入无效，跳过误差分析")
    
    # 等待用户确认继续
    print("\n是否继续测试Y轴？(y/N): ", end="")
    user_input = input().strip().lower()
    
    if user_input == 'y':
        # 测试Y轴正向运动
        print(f"\n--- Y轴正向运动 {test_distance}mm ---")
        y_success = controller.move_relative_mm(
            axis=1,
            distance_mm=test_distance,
            max_speed=3000,
            acceleration=2000,
            deceleration=2000,
            timeout_seconds=30.0
        )
        
        print(f"Y轴运动结果: {'成功' if y_success else '失败'}")
        
        # 获取Y轴运动后位置
        y_pos_after = controller.get_current_position_mm(1)
        y_actual_distance = y_pos_after - y_pos
        print(f"Y轴运动前位置: {y_pos:.3f}mm")
        print(f"Y轴运动后位置: {y_pos_after:.3f}mm")
        print(f"Y轴实际运动距离: {y_actual_distance:.3f}mm")
        print(f"Y轴目标运动距离: {test_distance:.3f}mm")
        print(f"Y轴运动误差: {abs(y_actual_distance - test_distance):.3f}mm")
        
        if y_success:
            print("\n请手动测量Y轴的实际运动距离，然后按Enter继续...")
            user_measurement = input("请输入实际测量的Y轴运动距离（mm）: ")
            try:
                measured_distance = float(user_measurement)
                software_error = abs(y_actual_distance - test_distance)
                hardware_error = abs(measured_distance - test_distance)
                print(f"软件计算误差: {software_error:.3f}mm")
                print(f"实际测量误差: {hardware_error:.3f}mm")
                print(f"软件与实测差异: {abs(y_actual_distance - measured_distance):.3f}mm")
            except ValueError:
                print("输入无效，跳过误差分析")
        
        # 比较X轴和Y轴的运动精度
        if x_success and y_success:
            print(f"\n--- X轴与Y轴运动精度比较 ---")
            print(f"X轴软件计算运动距离: {x_actual_distance:.3f}mm")
            print(f"Y轴软件计算运动距离: {y_actual_distance:.3f}mm")
            print(f"X轴与Y轴运动距离差异: {abs(x_actual_distance - y_actual_distance):.3f}mm")
            
            # 检查是否需要不同的电机参数
            distance_diff_ratio = abs(x_actual_distance - y_actual_distance) / test_distance
            if distance_diff_ratio > 0.02:  # 超过2%差异
                print(f"⚠️  警告：X轴和Y轴运动距离差异较大（{distance_diff_ratio:.1%}）")
                print("   可能需要为X轴和Y轴设置不同的电机参数")
            else:
                print(f"✓ X轴和Y轴运动精度一致（差异{distance_diff_ratio:.1%}）")
    
    # 测试返回原点
    print("\n是否测试返回原点功能？(y/N): ", end="")
    user_input = input().strip().lower()
    
    if user_input == 'y':
        print("\n--- 返回原点测试 ---")
        
        # X轴返回原点
        if x_success:
            print("X轴返回原点...")
            x_return_success = controller.move_absolute_mm(
                axis=0,
                position_mm=x_pos,  # 返回初始位置
                max_speed=3000,
                acceleration=2000,
                deceleration=2000,
                timeout_seconds=30.0
            )
            print(f"X轴返回原点: {'成功' if x_return_success else '失败'}")
            
            if x_return_success:
                x_final_pos = controller.get_current_position_mm(0)
                print(f"X轴最终位置: {x_final_pos:.3f}mm")
                print(f"X轴位置误差: {abs(x_final_pos - x_pos):.3f}mm")
        
        # Y轴返回原点
        if 'y_success' in locals() and y_success:
            print("Y轴返回原点...")
            y_return_success = controller.move_absolute_mm(
                axis=1,
                position_mm=y_pos,  # 返回初始位置
                max_speed=3000,
                acceleration=2000,
                deceleration=2000,
                timeout_seconds=30.0
            )
            print(f"Y轴返回原点: {'成功' if y_return_success else '失败'}")
            
            if y_return_success:
                y_final_pos = controller.get_current_position_mm(1)
                print(f"Y轴最终位置: {y_final_pos:.3f}mm")
                print(f"Y轴位置误差: {abs(y_final_pos - y_pos):.3f}mm")
    
    # 清理资源
    controller.cleanup()
    print("\n运动控制测试完成")
    return True


if __name__ == "__main__":
    test_movement_control()
